interface Strategy {
  name: string
}

interface Signal {
  shouldTrade: boolean
  action: 'BUY' | 'SELL' | 'HOLD'
  reason: string
  confidence: number
  expirySeconds?: number // dynamically computed
}

interface Balance {
  isDemo: number
  balance: number
}

// Formatted version of the data
interface ChartInfo {
  chartID: string
  settings: ChartSettings
}

// Raw version of the data
interface ChartsData {
  chart_id: string
  settings: ChartSettings | string
}

interface ChartSettings {
  chartId: string
  chartType: number
  chartPeriod: number
  candlesTimer: boolean
  symbol: string
  demoDealAmount: number
  liveDealAmount: number
  enabledTradeMonitor: boolean
  enabledRatingWidget: boolean
  isVisible: boolean
  fastTimeframe: number
  enabledAutoscroll: boolean
  enabledGridSnap: boolean
  minimizedTradePanel: boolean
  fastCloseAt: number
  enableQuickAutoOffset: boolean
  quickAutoOffsetValue: number
  showArea: boolean
  percentAmount: number
}

interface TradeSettings {
  tradeCapital?: number
  tradeAmount?: number
  expiry?: string
  confidenceThreshold?: number
  profitMargin?: number
  riskTolerance?: number
  leverage?: number
  stopLoss?: number
  takeProfit?: number
  expirySeconds?: number // User-defined expiry
  candlePeriodSeconds?: number // Chart period
  customRules?: {
    name: string
    params: Record<string, unknown>
  }[]
}

interface OpenOrderPayload {
  action: 'call' | 'put'
  amount: number
  asset: string
  isDemo: number
  optionType: number
  requestId: number
  time: number
}

interface OpenOrderResponse {
  id: string
  openTime: string
  closeTime: string
  openTimestamp: number
  closeTimestamp: number
  uid: number
  isDemo: number
  amount: number
  profit: number
  percentProfit: number
  percentLoss: number
  openPrice: number
  copyTicket: string
  closePrice: number
  command: number
  asset: string
  requestId: number
  openMs: number
  optionType: number
  isCopySignal: boolean
  currency: string
}
